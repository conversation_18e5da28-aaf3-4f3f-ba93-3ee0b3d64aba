import styles from './WriterBlock.module.css';

interface WriterBlockProps {
    blogDetails: {
        writerImage?: string;
        writerName?: string;
        writerDesignation?: string;
        totalBlogs?: number;
        writerShortname?: string;
    };
}

const WriterBlock = ({ blogDetails }: WriterBlockProps) => {
    console.log("check blog details", blogDetails);

    return (
        <div className={`p-6 ${styles.cardShadow} rounded-[15px] mt-[50px]`}>
            <div className="flex items-center gap-4">
                <div className="w-[61px] h-[61px] bg-blue-600 rounded-full flex items-center justify-center">
                    {blogDetails?.writerImage ? (
                        <img
                            src={blogDetails?.writerImage}
                            alt="Writer image"
                            className="rounded-full w-full h-full object-cover"
                        />
                    ) : (
                        <span className="text-white font-bold text-xl">
                            {blogDetails?.writerName?.charAt(0)?.toUpperCase() || 'P'}
                        </span>
                    )}
                </div>
                <div className="text-[#383838] flex flex-col gap-1 justify-center">
                    <h1 className="text-[24px] font-general-medium leading-[28px]">
                        {blogDetails?.writerName || 'Picklebay'}
                    </h1>
                    <p className="text-[14px] font-general text-gray-600">
                        {blogDetails?.totalBlogs || 3} Articles
                    </p>
                </div>
            </div>
        </div>
    );
};

export default WriterBlock;
