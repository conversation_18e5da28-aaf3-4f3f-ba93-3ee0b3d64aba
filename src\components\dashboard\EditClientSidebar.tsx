import { X } from "@phosphor-icons/react";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import Input from "../common/Input";
import SelectDropdown from "../common/SelectDropdown";
import Button from "../common/Button";
import {
  getClientById,
  putclientById,
  useGetTimeZone,
} from "@/services/clients.service";
import { useGetSettingData } from "@/services/setting.service";
import TimeZoneDrop from "./common/TimeZoneDrop";
import endpoints from "@/utils/endpoints";
import { fetcher } from "@/utils/axios";
import { mutate } from "swr";
import toast from "react-hot-toast";
const genderOption = [
  "Cisgender Male",
  "Cisgender Female",
  "Transgender",
  "Non-Binary",
  "Prefer not to say",
];

interface ClientData {
  client: {
    name: string;
    phone: string;
    age: number;
    gender: string;
    defaultTimezone: string;
    defaultSessionAmount: number;
    isActive: boolean;
    inactiveClient: boolean;
    fromPublicCalender?: boolean;
  };
}
const EditClientSidebar: React.FC<{
  isEditClient: boolean;
  setIsEditClient: (value: boolean) => void;
  singleClientById: string;
  query: string;
  setIsConfirmation: (value: boolean) => void;
  inactiveClient: boolean;
  setInactiveClient: (value: boolean) => void;
  // singleClientsData: any;
}> = ({
  isEditClient,
  setIsEditClient,
  singleClientById,
  query,
  setIsConfirmation,
  inactiveClient,
  setInactiveClient,
  // singleClientsData,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [clientData, setClientData] = useState<ClientData | null>(null);

  const { timeZoneData } = useGetTimeZone();
  const { therapistData } = useGetSettingData();

  // Initialize form with fetched data
  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      name: clientData?.client?.name || "",
      phone: clientData?.client?.phone || "",
      age: clientData?.client?.age?.toString() || "",
      gender: clientData?.client?.gender || "",
      defaultTimezone: clientData?.client?.defaultTimezone || "",
      defaultSessionAmount: "",
      isActive:
        clientData?.client?.isActive !== undefined
          ? clientData.client.isActive
          : true,
    },
    validationSchema: Yup.object({
      name: Yup.string().required("First name is required"),
      age: Yup.number()
        .positive("Age must be a positive number")
        .typeError("age must be a number"),
      phone: Yup.string()
        .matches(/^[0-9]+$/, "Only digits are allowed")
        .min(10, "Mobile number must be at least 10 digits")
        .max(10, "Mobile number maximum 10 digits"),
      defaultTimezone: Yup.string().required("Time Zone is required"),
      defaultSessionAmount: Yup.number()
        .positive("Amount must be positive.")
        .typeError("Default Session Amount must be a number")
        .required("Default Session Amount is required")
        .test("fee-range", function(value) {
          const minFee = therapistData?.minFee;
          const maxFee = therapistData?.maxFee;
          const isFromPublicCalendar = clientData?.client?.fromPublicCalender;

          // Only validate range for public calendar patients and when user enters an amount
          if (!isFromPublicCalendar || !minFee || !maxFee || !value) return true;

          if (value < minFee) {
            return this.createError({
              message: `Amount must be at least ₹${minFee}`
            });
          }

          if (value > maxFee) {
            return this.createError({
              message: `Amount cannot exceed ₹${maxFee}`
            });
          }

          return true;
        }),
    }),
    onSubmit: async (values) => {
      setIsSubmitting(true);
      try {
        if (
          formik.values.isActive !== formik.initialValues.isActive &&
          !formik.values.isActive
        ) {
          setIsConfirmation(true); // Open modal if isActive has changed
        } else {
          const toastId = toast.loading(`Editing ${values.name}...`, {
            duration: Infinity,
          });

          const data = await putclientById(singleClientById, values, toastId);
          if (data) {
            setIsEditClient(false);

            const url = `${endpoints.clients.clients}?${query}`;

            mutate(url, async () => {
              // This callback can be used to fetch updated data if needed
              await fetcher(url);
            });
          }
        }
      } catch (error) {
        console.error("Error updating client:", error);
      } finally {
        setIsSubmitting(false);
      }
    },
  });

  useEffect(() => {
    const updateClientStatus = async () => {
      if (inactiveClient) {
        const toastId = toast.loading(`Deactivating ${formik.values.name}...`, {
          duration: Infinity,
        });
        const data = await putclientById(
          singleClientById,
          formik.values,
          toastId
        );
        if (data) {
          setIsEditClient(false);
          setIsConfirmation(false);
          setInactiveClient(false);
          const url = `${endpoints.clients.clients}?${query}`;

          mutate(url, async () => {
            // This callback can be used to fetch updated data if needed
            await fetcher(url);
          });
        }
      }
    };

    updateClientStatus();
  }, [inactiveClient]);

  useEffect(() => {
    async function fetchClientData() {
      if (singleClientById && isEditClient) {
        const response = await getClientById(singleClientById);
        setClientData(response);
      }
    }
    fetchClientData();
  }, [singleClientById, isEditClient]);

  useEffect(() => {
    if (isEditClient) document.body.style.overflow = "hidden";
    else document.body.style.overflow = "auto";

    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isEditClient]);

  return (
    <div
      className={`fixed w-full h-full bg-black/20 top-0 left-0 z-[999] ${
        isEditClient ? "visible" : "invisible"
      }`}
      onClick={() => {
        setIsEditClient(false);
        formik.resetForm();
      }}
    >
      <div
        className={`max-w-[416px] w-full bg-white absolute top-0 right-0 h-full transition-all duration-300 ${
          isEditClient ? "translate-x-0" : "translate-x-full"
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="relative flex flex-col h-[100svh] sm:h-screen overflow-y-auto">
          {/* side bar header */}
          <div className="px-5 py-3.5 shadow-[0px_4px_12px_0px_#0000000F] flex justify-between items-center sticky top-0">
            <h3 className="text-lg font-medium text-[#242424]">Edit Patient</h3>
            <button
              onClick={() => {
                setIsEditClient(false);
                formik.resetForm();
              }}
            >
              <X size={20} />
            </button>
          </div>

          {/* Loader overlay */}
          {(isSubmitting || inactiveClient) && (
            <div className="absolute inset-0 flex justify-center items-center bg-black/50 z-[1000]">
              <div className="spinner-border animate-spin h-12 w-12 border-4 border-t-transparent border-white rounded-full"></div>
            </div>
          )}
          {/* content */}
          <div className="p-5 flex-1 overflow-auto">
            <form
              onSubmit={formik.handleSubmit}
              className="grid grid-cols-2 gap-5"
            >
              <div className="col-span-2">
                <label className="text-sm/5 text-primary font-medium">
                  Full Name <span className="text-red-600">*</span>
                </label>
                <Input
                  type="text"
                  placeholder="Enter First Name"
                  {...formik.getFieldProps("name")}
                />
                {formik.touched.name && formik.errors.name ? (
                  <div className="text-red-500 text-sm">
                    {formik.errors.name}
                  </div>
                ) : null}
              </div>
              {/* <div>
                <label className="text-sm/5 text-primary font-medium">
                  Last Name
                </label>
                <Input
                  type="text"
                  placeholder="Enter Last Name"
                  {...formik.getFieldProps("last_name")}
                />
                {formik.touched.last_name && formik.errors.last_name ? (
                  <div className="text-red-500 text-sm">
                    {formik.errors.last_name}
                  </div>
                ) : null}
              </div> */}

              <div className="col-span-2">
                <label className="text-sm/5 text-primary font-medium">
                  Mobile Number
                </label>
                <Input
                  type="tel" 
                  placeholder="Enter Number"
                  {...formik.getFieldProps("phone")}
                />
                {formik.touched.phone && formik.errors.phone ? (
                  <div className="text-red-500 text-sm">
                    {formik.errors.phone}
                  </div>
                ) : null}
              </div>
              <div>
                <label className="text-sm/5 text-primary font-medium">
                  Age
                </label>
                <Input
                  type="text" // Change to text to keep it as a string
                  placeholder="Enter Age"
                  {...formik.getFieldProps("age")}
                />
                {formik.touched.age && formik.errors.age ? (
                  <div className="text-red-500 text-sm">
                    {formik.errors.age}
                  </div>
                ) : null}
              </div>
              <div>
                <label className="text-sm/5 text-primary font-medium">
                  Gender
                </label>
                <SelectDropdown
                  options={genderOption}
                  value={formik.values.gender}
                  onChange={(value: unknown) =>
                    formik.setFieldValue("gender", value as string)
                  }
                  placeholder="Select ..."
                />
                {formik.touched.gender && formik.errors.gender ? (
                  <div className="text-red-500 text-sm">
                    {formik.errors.gender}
                  </div>
                ) : null}
              </div>
              {/* <div className="col-span-2">
                <label className="text-sm/5 text-primary font-medium">
                  Default Payment Amount
                </label>
                <Input
                  type="number"
                  placeholder="0"
                  icon="rup"
                  {...formik.getFieldProps("amount")}
                />
                {formik.touched.amount && formik.errors.amount ? (
                  <div className="text-red-500 text-sm">
                    {formik.errors.amount}
                  </div>
                ) : null}
              </div> */}
              <div className="col-span-2">
                <label className="text-sm/5 text-primary font-medium">
                  Default Time Zone <span className="text-red-600">*</span>
                </label>
                <TimeZoneDrop
                  options={timeZoneData}
                  value={formik.values.defaultTimezone}
                  onChange={(value) =>
                    formik.setFieldValue("defaultTimezone", value.value)
                  }
                  placeholder="Select ..."
                />
                {formik.touched.defaultTimezone &&
                formik.errors.defaultTimezone ? (
                  <div className="text-red-500 text-sm">
                    {formik.errors.defaultTimezone}
                  </div>
                ) : null}
              </div>
              <div className="col-span-2">
                <label className="text-sm/5 text-primary font-medium">
                  Default Session Amount <span className="text-red-600">*</span>
                </label>
                {clientData?.client?.fromPublicCalender && therapistData?.minFee && therapistData?.maxFee && (
                  <div className="text-xs text-gray-600 mb-1">
                    Range: ₹{therapistData.minFee} - ₹{therapistData.maxFee}
                  </div>
                )}
                <Input
                  type="text"
                  placeholder={
                    clientData?.client?.fromPublicCalender && therapistData?.minFee && therapistData?.maxFee
                      ? `Enter amount (₹${therapistData.minFee} - ₹${therapistData.maxFee})`
                      : "Enter Amount"
                  }
                  {...formik.getFieldProps("defaultSessionAmount")}
                />
                {formik.touched.defaultSessionAmount &&
                formik.errors.defaultSessionAmount ? (
                  <div className="text-red-500 text-sm">
                    {formik.errors.defaultSessionAmount}
                  </div>
                ) : null}
              </div>
              <div>
                <p className="text-sm/5 text-primary font-medium">
                  Patient Status <span className="text-red-600">*</span>
                </p>
                <div className="flex gap-5 pt-2">
                  <label className="flex items-center gap-1.5 text-xs text-primary">
                    <input
                      type="radio"
                      name="isActive"
                      value="true" // Set value to string "true"
                      checked={formik.values.isActive === true} // Check against boolean
                      onChange={() => formik.setFieldValue("isActive", true)} // Set as boolean
                      className="w-3.5 h-3.5"
                    />
                    Active
                  </label>
                  <label className="flex items-center gap-1.5 text-xs text-primary">
                    <input
                      type="radio"
                      name="isActive"
                      value="false" // Set value to string "false"
                      checked={!formik.values.isActive}
                      // Check against boolean
                      onChange={() => formik.setFieldValue("isActive", false)} // Set as boolean
                      className="w-3.5 h-3.5"
                    />
                    Inactive
                  </label>
                </div>
              </div>
            </form>
          </div>

          {/* side bar footer */}
          <div className="bg-white shadow-[0px_4px_43.4px_0px_#0000001A] px-5 py-2.5 grid grid-cols-2 gap-5 sticky bottom-0 z-10">
            <Button
              onClick={() => {
                setIsEditClient(false);
                formik.resetForm();
              }}
              variant="outlinedGreen"
            >
              Cancel
            </Button>
            <Button
              onClick={formik.handleSubmit}
              variant="filledGreen"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditClientSidebar;
