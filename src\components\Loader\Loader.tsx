interface LoaderProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
}

const Loader = ({ size = 'md', color = 'purple' }: LoaderProps) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const colorClasses = {
    purple: 'border-purple-600',
    loading: 'border-blue-600',
    default: 'border-gray-600'
  };

  const borderColor = colorClasses[color as keyof typeof colorClasses] || colorClasses.default;

  return (
    <div className={`${sizeClasses[size]} border-2 ${borderColor} border-t-transparent rounded-full animate-spin`}></div>
  );
};

export default Loader;
