"use client";

import { nanoid } from 'nanoid';
import { useRef, useState, useEffect } from 'react';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import { Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import JournalCard from '../JournalCard/JournalCard';
import WriterB<PERSON> from '../WriterBlock/WriterBlock';

interface MoreFromAuthorSectionProps {
    currentBlogId: string;
}

interface BlogData {
    _id: string;
    blogName: string;
    description: string;
    handle: string;
    featureImage?: string;
    isVisible: boolean;
    content?: string;
    publishedDate?: string;
}

// Dummy data for demonstration
const dummyAuthorBlogs: BlogData[] = [
    {
        _id: '3',
        blogName: 'The Importance of Footwork in Pickleball and How to Improve It',
        description: '<p>It all starts with the right stance. Your feet should be shoulder-width apart, knees...</p>',
        handle: 'pickleball-footwork-guide',
        featureImage: '/assets/images/client/hero-animation-one.gif',
        isVisible: true,
        publishedDate: '2024-01-10',
    },
    {
        _id: '4',
        blogName: 'Mastering the Dink: Developing a Killer Soft Game',
        description: '<p>Pickleball is not just about power; it\'s also about finesse and strategy...</p>',
        handle: 'mastering-the-dink',
        featureImage: '/assets/images/client/different_vector_one.png',
        isVisible: true,
        publishedDate: '2024-01-05',
    },
    {
        _id: '5',
        blogName: 'Pickleball Strategy: Positioning and Court Awareness',
        description: '<p>So you\'ve got the basics down and you\'re ready to take your game to the next level...</p>',
        handle: 'pickleball-strategy-positioning',
        featureImage: '/assets/images/client/hero-animation-one.gif',
        isVisible: true,
        publishedDate: '2024-01-01',
    }
];

const dummyAuthorDetails = {
    writerImage: '/assets/images/client/hero-animation-one.gif',
    writerName: 'Picklebay',
    writerDesignation: 'Journal',
    totalBlogs: 3,
    writerShortname: 'Expert pickleball coach and sports enthusiast with over 10 years of experience in competitive play and coaching.'
};

const MoreFromAuthorSection = ({ currentBlogId }: MoreFromAuthorSectionProps) => {
    const swiperRef = useRef<any>(null);
    const [authorBlogs, setAuthorBlogs] = useState<BlogData[]>([]);
    const [authorDetails, setAuthorDetails] = useState(dummyAuthorDetails);

    const handlePrevClick = () => {
        if (swiperRef.current) {
            swiperRef.current.slidePrev();
        }
    };

    const handleNextClick = () => {
        if (swiperRef.current) {
            swiperRef.current.slideNext();
        }
    };

    useEffect(() => {
        // Filter out the current blog and get other blogs from the same author
        const filteredBlogs = dummyAuthorBlogs.filter(blog => blog._id !== currentBlogId);
        setAuthorBlogs(filteredBlogs);
    }, [currentBlogId]);

    // Don't render if no other blogs from this author
    if (authorBlogs.length === 0) {
        return null;
    }

    return (
        <div className="mt-12">
            {/* Writer Block */}
            <WriterBlock blogDetails={authorDetails} />
            
            {/* More From This Author Carousel */}
            <div className="py-10 journal-listing">
                <div className='flex items-center justify-between md:py-[35px] py-[25px]'>
                    <p className='font-general font-medium text-sm md:text-base text-gray-500'>
                        More From This Author
                    </p>
                    <div className="flex items-center justify-between gap-3">
                        <button
                            className="flex w-[35px] h-[35px] items-center justify-center p-3 border rounded-full cursor-pointer hover:bg-gray-100"
                            onClick={handlePrevClick}
                        >
                            <svg 
                                width="16" 
                                height="16" 
                                viewBox="0 0 24 24" 
                                fill="none" 
                                className="rotate-180"
                            >
                                <path 
                                    d="M9 18L15 12L9 6" 
                                    stroke="currentColor" 
                                    strokeWidth="2" 
                                    strokeLinecap="round" 
                                    strokeLinejoin="round"
                                />
                            </svg>
                        </button>
                        <button
                            className="flex w-[35px] h-[35px] items-center justify-center p-3 border rounded-full cursor-pointer hover:bg-gray-100"
                            onClick={handleNextClick}
                        >
                            <svg 
                                width="16" 
                                height="16" 
                                viewBox="0 0 24 24" 
                                fill="none"
                            >
                                <path 
                                    d="M9 18L15 12L9 6" 
                                    stroke="currentColor" 
                                    strokeWidth="2" 
                                    strokeLinecap="round" 
                                    strokeLinejoin="round"
                                />
                            </svg>
                        </button>
                    </div>
                </div>

                <div className="">
                    <div className="">
                        <Swiper
                            onSwiper={(swiper) => {
                                swiperRef.current = swiper;
                            }}
                            modules={[Navigation]}
                            spaceBetween={15}
                            breakpoints={{
                                320: {
                                    slidesPerView: 1.2,
                                },
                                640: {
                                    slidesPerView: 2.2,
                                },
                            }}
                        >
                            {authorBlogs.map((blog) => (
                                <SwiperSlide key={nanoid()}>
                                    <JournalCard data={blog} />
                                </SwiperSlide>
                            ))}
                        </Swiper>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default MoreFromAuthorSection;
