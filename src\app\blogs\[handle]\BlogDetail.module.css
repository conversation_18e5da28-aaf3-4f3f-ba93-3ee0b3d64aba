/* Blog Detail Page Styles */
.blogContent {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.8;
  color: #374151;
}

.blogContent h1 {
  font-size: 18px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

@media (min-width: 768px) {
  .blogContent h1 {
    font-size: 32px;
  }
}

.blogContent h2 {
  font-size: 17px;
  font-weight: 600;
  color: #1f2937;
  margin-top: 3rem;
  margin-bottom: 1.5rem;
  line-height: 1.3;
}

@media (min-width: 768px) {
  .blogContent h2 {
    font-size: 28px;
  }
}

.blogContent h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  line-height: 1.4;
}

@media (min-width: 768px) {
  .blogContent h3 {
    font-size: 24px;
  }
}

.blogContent h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin-top: 2rem;
  margin-bottom: 0.75rem;
}

.blogContent p {
  font-size: 14px;
  margin-bottom: 1rem;
  color: #4b5563;
}

@media (min-width: 768px) {
  .blogContent p {
    font-size: 20px;
  }
}

.blogContent ul,
.blogContent ol {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.blogContent li {
  font-size: 1.125rem;
  margin-bottom: 0.75rem;
  color: #4b5563;
  line-height: 1.7;
}

.blogContent li strong {
  color: #1f2937;
  font-weight: 600;
}

.blogContent blockquote {
  border-left: 4px solid #8b5cf6;
  padding-left: 1.5rem;
  margin: 2rem 0;
  font-style: italic;
  color: #6b7280;
  background-color: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.5rem;
}

.blogContent a {
  color: #8b5cf6;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.blogContent a:hover {
  color: #7c3aed;
}

.blogContent code {
  background-color: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: #dc2626;
}

.blogContent pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.blogContent pre code {
  background-color: transparent;
  color: inherit;
  padding: 0;
}

.blogContent img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1.5rem 0;
}

.blogContent hr {
  border: none;
  height: 1px;
  background-color: #e5e7eb;
  margin: 3rem 0;
}

/* Tag styles */
.tagContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 2rem;
}

.tag {
  background-color: #f3f4f6;
  color: #6b7280;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  transition: all 0.2s ease;
}

.tag:hover {
  background-color: #e5e7eb;
  color: #374151;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .blogContent h1 {
    font-size: 2rem;
  }
  
  .blogContent h2 {
    font-size: 1.75rem;
  }
  
  .blogContent h3 {
    font-size: 1.375rem;
  }
  
  .blogContent p,
  .blogContent li {
    font-size: 1rem;
  }
  
  .blogContent blockquote {
    padding: 1rem;
    margin: 1.5rem 0;
  }
}

/* Subheading section styles - to handle HTML content in subheading */
.subheadingContent h1,
.subheadingContent h2,
.subheadingContent h3,
.subheadingContent h4,
.subheadingContent h5,
.subheadingContent h6 {
  font-size: 14px;
  font-weight: inherit;
  color: #374151;
  margin: 0 0 0.5rem 0;
  line-height: 1.6;
}

@media (min-width: 768px) {
  .subheadingContent h1,
  .subheadingContent h2,
  .subheadingContent h3,
  .subheadingContent h4,
  .subheadingContent h5,
  .subheadingContent h6 {
    font-size: 20px;
  }
}

.subheadingContent p {
  font-size: 12px;
  font-weight: inherit;
  color: #374151;
  margin: 0 0 0.5rem 0;
  line-height: 1.6;
}

@media (min-width: 768px) {
  .subheadingContent p {
    font-size: 16px;
  }
}

.subheadingContent p:last-child {
  margin-bottom: 0;
}

/* Animation for content loading */
.contentFadeIn {
  animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}


