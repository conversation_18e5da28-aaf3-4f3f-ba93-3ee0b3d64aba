"use client";

import Image from 'next/image';
import { useState, useEffect } from 'react';
import { useHomepageJournal } from '@/hooks/BlogHooks';
import DOMPurify from 'dompurify';



const BlogCard = ({ data }: { data: any }) => {
  const handleKnowMoreClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const blogHandle = data.handle || data.slug || data._id;
    window.location.href = `/blogs/${blogHandle}`;
  };

  return (
    <div
      className="bg-white rounded-[20px] shadow-md hover:shadow-xl transition-all duration-300 ease-in-out overflow-hidden group relative w-full max-w-[275px] mx-auto"
      style={{ height: '379px' }}
    >
      {/* Image */}
      <div className="relative w-full" style={{ height: '203px' }}>
        {(data.featureImage || data.image || data.thumbnail) ? (
          <Image
            src={data.featureImage || data.image || data.thumbnail}
            alt={data.blogName || data.title || data.name || 'Blog'}
            fill
            className="object-cover"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
            <span className="text-white font-medium">Blog Image</span>
          </div>
        )}
      </div>

      {/* Know More Button - perfectly centered overlapping image and content */}
      <div
        className="absolute left-1/2 z-10"
        style={{
          top: '203px',
          transform: 'translate(-50%, -50%)',
        }}
      >
        <button
          onClick={handleKnowMoreClick}
          className="bg-[#E9FA6F] text-black px-4 sm:px-6 py-2 rounded-full text-[11px] sm:text-[13px] font-semibold font-general flex items-center gap-1 hover:bg-[#d9ea5f] transition-all transform hover:scale-105 active:scale-95 border-[4px] sm:border-[6px] border-white whitespace-nowrap"
        >
          <span>Know More</span>
          <div className="bg-[#FF9CC7] rounded-full p-1 ml-1 transition-transform duration-300 group-hover:translate-x-1">
            <svg width="14" height="14" viewBox="0 0 16 16" fill="none" className="text-black">
              <path d="M6 4L10 8L6 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </div>
        </button>
      </div>

      {/* Content */}
      <div className="p-4 sm:p-6 pt-6 sm:pt-8">
        <h3 className="text-base sm:text-lg font-semibold text-gray-800 mb-2 sm:mb-3 line-clamp-2 font-general">
          {data.blogName || data.title || data.name || 'Untitled Blog'}
        </h3>
        <div
          className="text-gray-600 text-xs sm:text-sm line-clamp-3 font-general"
          dangerouslySetInnerHTML={{
            __html: DOMPurify.sanitize(data.description || data.excerpt || data.content || 'No description available')
          }}
        />
      </div>
    </div>
  );
};

const BlogCarouselSection = () => {
  const { data: blogs, isLoading, isError } = useHomepageJournal();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [itemsPerView, setItemsPerView] = useState(4);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);

  // Responsive items per view logic
  useEffect(() => {
    const updateItemsPerView = () => {
      if (window.innerWidth < 640) {
        setItemsPerView(1); // Mobile: 1 item
      } else if (window.innerWidth < 1024) {
        setItemsPerView(2); // Tablet: 2 items
      } else if (window.innerWidth < 1280) {
        setItemsPerView(3); // Small desktop: 3 items
      } else {
        setItemsPerView(4); // Large desktop: 4 items
      }
    };

    updateItemsPerView();
    window.addEventListener('resize', updateItemsPerView);
    return () => window.removeEventListener('resize', updateItemsPerView);
  }, []);

  // Don't render anything while loading to prevent flash
  if (isLoading) {
    return null;
  }

  // Filter visible blogs - ensure blogs is an array
  console.log('Raw blogs data:', blogs);
  if (Array.isArray(blogs) && blogs.length > 0) {
    console.log('First blog item:', blogs[0]);
    console.log('Blog properties:', Object.keys(blogs[0]));
  }

  const visibleBlogs = Array.isArray(blogs) ? blogs.filter((blog: any) => {
    console.log('Checking blog visibility:', blog, 'isVisible:', blog.isVisible);
    // Only show blogs that are explicitly visible (isVisible === true)
    return blog.isVisible === true;
  }) : [];

  console.log('Filtered visible blogs:', visibleBlogs);

  // If no visible blogs, hide the section completely
  if (visibleBlogs.length === 0) {
    console.log('No visible blogs available, hiding blog section');
    return null;
  }

  // Use only visible blogs from API
  const displayBlogs = visibleBlogs;

  // Carousel navigation
  const maxIndex = Math.max(0, displayBlogs.length - itemsPerView);

  const goToPrevious = () => {
    setCurrentIndex(prev => {
      if (prev === 0) {
        return maxIndex;
      }
      return prev - 1;
    });
  };

  const goToNext = () => {
    setCurrentIndex(prev => {
      if (prev >= maxIndex) {
        return 0;
      }
      return prev + 1;
    });
  };

  // Touch handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(0); // Reset touchEnd
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && currentIndex < maxIndex) {
      goToNext();
    }
    if (isRightSwipe && currentIndex > 0) {
      goToPrevious();
    }
  };

  return (
    <>

      <div
        className="py-16 blog-carousel"
        style={{
          background: "#FF9CC7",
        }}
      >
        {/* Header */}
        <div className="max-w-[1299px] mx-auto px-4 sm:px-6 lg:px-12 mb-8 sm:mb-12">
          <div className="flex justify-between items-center">
            <h2 className="font-bold text-black font-general">
              <span className="font-roboto text-[18px] sm:text-[22px] md:text-[36px]">Related</span> <span className="font-new-elegance text-[32px] sm:text-[40px] md:text-[64px] italic pl-2">Blogs</span>
            </h2>

            {/* Navigation Buttons - Hide on mobile when only 1 item per view */}
            {displayBlogs.length > itemsPerView && itemsPerView > 1 && (
              <div className="flex gap-2">
                <button
                  onClick={goToPrevious}
                  className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-white text-black flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  <svg width="14" height="14" viewBox="0 0 16 16" fill="none" className="sm:w-4 sm:h-4">
                    <path d="M10 4L6 8L10 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </button>
                <button
                  onClick={goToNext}
                  className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-white text-black flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  <svg width="14" height="14" viewBox="0 0 16 16" fill="none" className="sm:w-4 sm:h-4">
                    <path d="M6 4L10 8L6 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Blog Carousel */}
        <div className="max-w-[1299px] mx-auto px-4 sm:px-6 lg:px-12">
          <div
            className="overflow-hidden"
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            <div
              className="flex transition-transform duration-700 ease-out gap-2 sm:gap-4 lg:gap-6"
              style={{
                transform: `translateX(-${currentIndex * (100 / itemsPerView)}%)`,
              }}
            >
              {/* Render all blogs for smooth sliding */}
              {displayBlogs.map((blog) => (
                <div
                  key={blog._id}
                  className="flex-shrink-0"
                  style={{
                    width: itemsPerView === 1
                      ? `calc(${100 / itemsPerView}% - 8px)`
                      : itemsPerView === 2
                      ? `calc(${100 / itemsPerView}% - 12px)`
                      : `calc(${100 / itemsPerView}% - 18px)`
                  }}
                >
                  <BlogCard data={blog} />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Navigation Dots for Mobile */}
        {itemsPerView === 1 && displayBlogs.length > 1 && (
          <div className="flex justify-center mt-6 gap-2">
            {Array.from({ length: displayBlogs.length }, (_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? 'bg-white scale-125'
                    : 'bg-white/50 hover:bg-white/75'
                }`}
              />
            ))}
          </div>
        )}

      </div>
    </>
  );
};

export default BlogCarouselSection;
