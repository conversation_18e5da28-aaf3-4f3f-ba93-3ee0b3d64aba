"use client";

import Link from "next/link";
import Image from "next/image";
import { useEffect, useState, Fragment } from "react";
import { useSearchParams } from "next/navigation";
import { Caveat } from "next/font/google";
import Login from "@/components/common/Login";

const caveat = Caveat({ subsets: ["latin"], weight: "700" });

// Separate component that uses useSearchParams
const ClientHeaderContent: React.FC = () => {
  const queryParams = useSearchParams();
  const [isScrolled, setIsScrolled] = useState(false);
  const [loginOpen, setLoginOpen] = useState(false);
  const [th_id, setTherapistId] = useState<string | null>(null);
  const [userStep, setUserStep] = useState<string | null>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const therapist_id = queryParams.get("q");
  const user_step = queryParams.get("step");
  const loginParam = queryParams.get("login");

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('header')) {
        setMobileMenuOpen(false);
      }
    };

    if (mobileMenuOpen) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [mobileMenuOpen]);

  useEffect(() => {
    if (therapist_id && user_step) {
      setTherapistId(therapist_id);
      setUserStep(user_step);
      setLoginOpen(true);
    }
  }, [therapist_id, user_step]);

  useEffect(() => {
    if (loginParam) {
      setLoginOpen(true);
    }

    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [loginParam]);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setMobileMenuOpen(false);
    }
  };

  return (
    <Fragment>
      <header
        className={`fixed top-0 z-50 transition-all duration-300 w-full mx-0 ${
          isScrolled ? "backdrop-blur-md shadow-md bg-white/95" : "bg-white"
        }`}
      >
        <div className="flex items-center justify-between px-6 py-4 max-w-[1299px] mx-auto">
          {/* Logo */}
          <Link href="/clients" className="flex items-center">
            <Image
              src="/assets/images/client/client-logo.svg"
              alt="Thought Pudding Logo"
              width={191}
              height={79}
              className="object-contain w-[120px] h-[49px] md:w-[150px] md:h-[62px] lg:w-[191px] lg:h-[79px]"
              priority
            />
          </Link>

          {/* Right Side - Navigation & Button & Hamburger for Mobile */}
          <div className="flex items-center gap-8">
            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center gap-[48px]">
              <button
                onClick={() => scrollToSection('why-different')}
                className="text-lg font-medium text-black hover:text-[#718FFF] transition-colors duration-200 cursor-pointer font-roboto"
              >
                About Us
              </button>
              <button
                onClick={() => scrollToSection('services')}
                className="text-lg font-medium text-black hover:text-[#718FFF] transition-colors duration-200 cursor-pointer font-roboto"
              >
                Services
              </button>
              <Link href="/blogs" className="text-lg font-medium text-black hover:text-[#718FFF] transition-colors duration-200 cursor-pointer font-roboto">
                Blogs
              </Link>
            </nav>

            {/* Hamburger Icon for Mobile */}
            <button
              className="lg:hidden flex flex-col justify-center items-center w-12 h-12 focus:outline-none hover:bg-gray-100 rounded-lg transition-colors"
              aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
              onClick={(e) => {
                e.stopPropagation();
                setMobileMenuOpen((v) => !v);
              }}
            >
              <span className={`block w-7 h-0.5 bg-black mb-1.5 rounded transition-all duration-200 ${
                mobileMenuOpen ? 'rotate-45 translate-y-2' : ''
              }`} />
              <span className={`block w-7 h-0.5 bg-black mb-1.5 rounded transition-all duration-200 ${
                mobileMenuOpen ? 'opacity-0' : ''
              }`} />
              <span className={`block w-7 h-0.5 bg-black rounded transition-all duration-200 ${
                mobileMenuOpen ? '-rotate-45 -translate-y-2' : ''
              }`} />
            </button>

            {/* Meet Our Therapists Button - Green Background with Caveat Font */}
            <button
              onClick={() => scrollToSection('about')}
              className="px-5 py-4 bg-[#E9FA6F] text-black rounded-[10px] font-bold hover:opacity-90 transition-all duration-200 shadow-sm hover:shadow-md hidden lg:block"
            >
              <span
                className={`text-sm ${caveat.className}`}
                style={{
                  fontSize: '18px'
                }}
              >
                Meet Our Therapists
              </span>
            </button>
          </div>
        </div>

        {/* Mobile Dropdown Menu */}
        {mobileMenuOpen && (
          <div className="lg:hidden absolute top-full right-0 left-0 bg-white shadow-lg z-50 border-t">
            <nav className="flex flex-col items-center py-4 gap-2">
              <button
                onClick={() => {
                  scrollToSection('why-different');
                  setMobileMenuOpen(false);
                }}
                className="w-full text-base font-medium text-black hover:text-[#718FFF] transition-colors duration-200 font-roboto py-2"
              >
                About Us
              </button>
              <button
                onClick={() => {
                  scrollToSection('services');
                  setMobileMenuOpen(false);
                }}
                className="w-full text-base font-medium text-black hover:text-[#718FFF] transition-colors duration-200 font-roboto py-2"
              >
                Services
              </button>
              <Link
                href="/blogs"
                onClick={() => setMobileMenuOpen(false)}
                className="w-full text-base font-medium text-black hover:text-[#718FFF] transition-colors duration-200 font-roboto py-2"
              >
                Blogs
              </Link>
              <button
                onClick={() => {
                  scrollToSection('about');
                  setMobileMenuOpen(false);
                }}
                className={`w-full px-5 py-4 bg-[#E9FA6F] text-black rounded-[10px] font-bold hover:opacity-90 transition-all duration-200 shadow-sm hover:shadow-md mt-2 ${caveat.className}`}
                style={{ fontSize: '18px' }}
              >
                Meet Our Therapists
              </button>
            </nav>
          </div>
        )}
      </header>

      {/* Login Modal */}
      <Login
        setLoginOpen={setLoginOpen}
        loginOpen={loginOpen}
        th_id={th_id}
        userStep={userStep}
      />
    </Fragment>
  );
};

// Main component that wraps the content with Suspense
const ClientHeader: React.FC = () => {
  return <ClientHeaderContent />;
};

export default ClientHeader;