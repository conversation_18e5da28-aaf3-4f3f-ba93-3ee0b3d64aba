"use client";

import { useBlogListing } from "../../hooks/BlogHooks";
import DOMPurify from 'dompurify';
import Loader from "../../components/Loader/Loader";
import ClientLayout from "../../layout/client/ClientLayout";
import ClientFooter from "../clients/Landing Page/Footer";
import Link from 'next/link';
import Image from 'next/image';

interface BlogData {
  _id: string;
  blogName: string;
  title?: string;
  name?: string;
  description: string;
  excerpt?: string;
  content?: string;
  handle: string;
  featureImage?: string;
  isVisible: boolean;
  publishedDate?: string;
}

const BlogCard = ({ data }: { data: BlogData }) => {
  const handleKnowMoreClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const blogHandle = data.handle || data._id;
    window.location.href = `/blogs/${blogHandle}`;
  };

  return (
    <div
      className="bg-white rounded-[20px] shadow-md hover:shadow-xl transition-all duration-300 ease-in-out overflow-hidden group relative"
      style={{ width: '275px', height: '379px' }}
    >
      {/* Image */}
      <div className="relative" style={{ width: '275px', height: '203px' }}>
        {(data.featureImage) ? (
          <Image
            src={data.featureImage}
            alt={data.blogName || 'Blog'}
            fill
            className="object-cover"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
            <span className="text-white font-medium">Blog Image</span>
          </div>
        )}
      </div>

      {/* Know More Button - perfectly centered overlapping image and content */}
      <div
        className="absolute left-1/2 z-10"
        style={{
          top: '203px',
          transform: 'translate(-50%, -50%)',
        }}
      >
        <button
          onClick={handleKnowMoreClick}
          className="bg-[#E9FA6F] text-black px-6 py-2 rounded-full text-[13px] font-semibold font-general flex items-center gap-1 hover:bg-[#d9ea5f] transition-all transform hover:scale-105 active:scale-95 border-[6px] border-white whitespace-nowrap"
        >
          <span>Know More</span>
          <div className="bg-[#FF9CC7] rounded-full p-1 ml-1 transition-transform duration-300 group-hover:translate-x-1">
            <svg width="14" height="14" viewBox="0 0 16 16" fill="none" className="text-black">
              <path d="M6 4L10 8L6 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </div>
        </button>
      </div>

      {/* Content */}
      <div className="p-6 pt-8">
        <h3 className="text-lg font-semibold text-gray-800 mb-3 line-clamp-2 font-general">
          {data.blogName || data.title || data.name || 'Untitled Blog'}
        </h3>
        <div
          className="text-gray-600 text-sm line-clamp-3 font-general"
          dangerouslySetInnerHTML={{
            __html: DOMPurify.sanitize(data.description || data.excerpt || data.content || 'No description available')
          }}
        />
      </div>
    </div>
  );
};

const BlogListing = () => {
  const { data, isLoading, isError, error } = useBlogListing();

  if(isLoading) {
    return (
      <ClientLayout>
        <div className="w-full h-screen flex justify-center items-center">
          <Loader />
        </div>
      </ClientLayout>
    );
  }

  if(isError) {
    return (
      <ClientLayout>
        <div className="w-full h-screen flex justify-center items-center">
          <h3 className="font-general">Something went wrong</h3>
        </div>
      </ClientLayout>
    );
  }

  return (
    <ClientLayout>
      <div className="min-h-screen">
        {/* Blog Section with Pink/Purple Gradient Background */}
        <div
          className="pt-[120px] pb-16"
          style={{
            background: "#FF9CC7",
          }}
        >
          <div className="max-w-[1299px] mx-auto px-6 lg:px-12">
            {/* Header */}
            <div className="mb-12">
              <h2 className="font-bold text-black font-general">
                <span className="font-roboto text-[22px] md:text-[36px]">Related</span> <span className="font-new-elegance text-[48px] md:text[64px] italic pl-2">Blogs</span>
              </h2>
            </div>

            {/* Blog Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 justify-items-center">
              {data && data.length > 0 ? (
                data.map((blog: BlogData) => (
                  <BlogCard key={blog._id} data={blog} />
                ))
              ) : (
                <div className="col-span-full text-center py-12">
                  <h3 className="text-xl font-semibold text-gray-700 mb-4">No blogs available</h3>
                  <p className="text-gray-600">Check back later for new content!</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <ClientFooter />
    </ClientLayout>
  );
};

export default BlogListing;
