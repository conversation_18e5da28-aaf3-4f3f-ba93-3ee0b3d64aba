"use client";

import { useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import ClientLayout from "../../../layout/client/ClientLayout";
import Loader from "../../../components/Loader/Loader";
import { useBlogDetail, useBlogSectionVisibility } from "../../../hooks/BlogHooks";
import BlogCarouselSection from "../../clients/Landing Page/BlogCarouselSection";
import ClientFooter from "../../clients/Landing Page/Footer";
import DOMPurify from 'dompurify';
import styles from './BlogDetail.module.css';
import { FaInstagram, FaLinkedinIn, FaYoutube, FaTwitter } from "react-icons/fa";



// Helper function to extract first paragraph
const getFirstParagraph = (content: string) => {
  if (!content) return '';

  // Remove HTML tags and get text content
  const textContent = content.replace(/<[^>]*>/g, ' ').trim();

  // Split by periods and take first sentence(s) that make a reasonable paragraph
  const sentences = textContent.split(/[.!?]+/);
  let firstParagraph = '';
  let wordCount = 0;

  for (let i = 0; i < sentences.length; i++) {
    const sentence = sentences[i].trim();
    if (sentence) {
      const words = sentence.split(' ').length;
      if (wordCount + words > 50 && firstParagraph) break; // Stop if we have enough content
      firstParagraph += sentence + '. ';
      wordCount += words;
    }
  }

  return `<p>${firstParagraph.trim()}</p>`;
};

// Helper function to get remaining content after first paragraph
const getRemainingContent = (content: string) => {
  if (!content) return '';

  // More aggressive cleaning of the content
  let cleanedContent = content
    // First, normalize all line breaks and whitespace
    .replace(/\r\n/g, '\n')
    .replace(/\r/g, '\n')
    // Remove all <br> tags completely since we're using paragraphs
    .replace(/<br\s*\/?>/gi, '')
    // Remove empty paragraphs (including those with just whitespace/nbsp)
    .replace(/<p[^>]*>(\s|&nbsp;)*<\/p>/gi, '')
    // Remove paragraphs that only contain line breaks
    .replace(/<p[^>]*>\s*(<br\s*\/?>)*\s*<\/p>/gi, '')
    // Clean up multiple consecutive newlines
    .replace(/\n{2,}/g, '\n')
    // Remove excessive whitespace between tags
    .replace(/>\s+</g, '><')
    // Clean up whitespace at the beginning and end of paragraphs
    .replace(/<p([^>]*)>\s+/gi, '<p$1>')
    .replace(/\s+<\/p>/gi, '</p>')
    // Remove any remaining standalone line breaks
    .replace(/^\s*\n+|\n+\s*$/g, '')
    // Ensure proper spacing between paragraphs by removing extra whitespace
    .replace(/<\/p>\s*<p/gi, '</p><p');

  // Debug: log the cleaned content to see what we're working with
  console.log('Original content:', content.substring(0, 200));
  console.log('Cleaned content:', cleanedContent.substring(0, 200));

  return cleanedContent;
};

const BlogDetailPage = () => {
  const params = useParams();
  const handle = params.handle as string;
  const { data: blog, isLoading, isError } = useBlogDetail(handle);
  const shouldShowBlogSection = useBlogSectionVisibility();

  // Debug: Log blog data to see available fields
  console.log('Blog data:', blog);
  console.log('Blog fields:', blog ? Object.keys(blog) : 'No blog data');
  console.log('Should show blog section:', shouldShowBlogSection);

  // Share functions
  const shareOnTwitter = () => {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent(`Check out this blog: "${blog?.blogName}"`);
    window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
  };

  const shareOnFacebook = () => {
    const url = encodeURIComponent(window.location.href);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
  };

  const shareOnLinkedIn = () => {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(blog?.blogName || '');
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}`, '_blank');
  };

  const shareOnWhatsApp = () => {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent(`Check out this blog: "${blog?.blogName}" ${window.location.href}`);
    window.open(`https://wa.me/?text=${text}`, '_blank');
  };

  if (isLoading) {
    return (
      <ClientLayout>
        <div className="w-full h-screen flex justify-center items-center">
          <Loader size="lg" color="loading" />
        </div>
      </ClientLayout>
    );
  }

  if (isError || !blog) {
    return (
      <ClientLayout>
        <div className="w-full h-screen flex justify-center items-center">
          <div className="text-center">
            <h3 className="text-xl font-medium text-gray-600 mb-4">Blog not found</h3>
            <Link
              href="/blogs"
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors inline-block"
            >
              Back to Blogs
            </Link>
          </div>
        </div>
      </ClientLayout>
    );
  }

  return (
    <ClientLayout>
      <div className="min-h-screen bg-white">
        {/* Header Section */}
        <div className="pt-[120px] pb-8">
          <div className="max-w-[1299px] mx-auto px-6 lg:px-12">
            {/* Back button */}
            <Link
              href="/blogs"
              className="mb-8 text-purple-600 hover:text-purple-800 transition-colors flex items-center gap-2 inline-flex font-medium font-general"
            >
              ← Back to Blogs
            </Link>

            {/* Blog Title */}
            <h1 className="text-[22px] md:text-[42px] font-bold text-gray-900 mb-6 leading-tight italic font-general">
              "{blog.blogName}"
            </h1>

            {/* Subheading */}
            {blog.blogSubheading && (
              <div
                className={`${styles.subheadingContent} font-normal text-gray-700 mb-8 leading-relaxed font-general`}
                dangerouslySetInnerHTML={{
                  __html: DOMPurify.sanitize(getRemainingContent(blog.blogSubheading))
                }}
              />
            )}

            {/* Published Date and Social Media Section */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 gap-4">
              {/* Left side - Published date and author */}
              <div className="flex flex-row gap-8 sm:gap-16 justify-between sm:justify-start">
                {/* Published Date */}
                <div className="flex flex-col flex-shrink-0">
                  <span className="text-[10px] md:text-[16px] font-medium text-gray-600 mb-1 font-general">Published on</span>
                  <span className="text-[12px] md:text-[18px] font-semibold text-gray-900 font-general">
                    {blog.publishedDate
                      ? new Date(blog.publishedDate).toLocaleDateString('en-US', {
                          day: '2-digit',
                          month: '2-digit',
                          year: 'numeric'
                        }).replace(/\//g, ' • ')
                      : '08 • 01 • 2024'
                    }
                  </span>
                </div>

                {/* Author */}
                <div className="flex flex-col flex-shrink-0">
                  <span className="text-[10px] md:text-[16px] font-medium text-gray-600 mb-1 font-general">Author</span>
                  <span className="text-[12px] md:text-[18px] font-semibold text-gray-900 font-general">
                    {blog?.writerName}
                  </span>
                </div>
              </div>

              {/* Right side - Social media share */}
              <div className="flex items-center gap-3">
                <span className="text-[10px] md:text-[16px] font-medium text-gray-600 font-general">Share on</span>
                <div className="flex gap-2">
                  <button
                    onClick={shareOnTwitter}
                    className="w-7 h-7 rounded-full bg-black text-white flex items-center justify-center hover:bg-gray-800 transition-colors"
                    aria-label="Share on X (Twitter)"
                  >
                    <FaTwitter size={12} />
                  </button>
                  <button
                    onClick={shareOnFacebook}
                    className="w-7 h-7 rounded-full bg-black text-white flex items-center justify-center hover:bg-gray-800 transition-colors"
                    aria-label="Share on Facebook"
                  >
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                  </button>
                  <button
                    onClick={shareOnLinkedIn}
                    className="w-7 h-7 rounded-full bg-black text-white flex items-center justify-center hover:bg-gray-800 transition-colors"
                    aria-label="Share on LinkedIn"
                  >
                    <FaLinkedinIn size={12} />
                  </button>
                  <button
                    onClick={shareOnWhatsApp}
                    className="w-7 h-7 rounded-full bg-black text-white flex items-center justify-center hover:bg-gray-800 transition-colors"
                    aria-label="Share on WhatsApp"
                  >
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Featured Image */}
        {blog.featureImage && (
          <div className="mb-12">
            <div className="max-w-[1299px] mx-auto px-6 lg:px-12">
              <div className="rounded-xl overflow-hidden shadow-lg">
                <Image
                  src={blog.featureImage}
                  alt={blog.blogName}
                  width={1211}
                  height={395}
                  className="w-[358px] md:w-[1211px] h-[499px] md:h-[395px] object-cover"
                />
              </div>
            </div>
          </div>
        )}

        {/* Remaining Blog Content */}
        <div className="mb-16">
          <div className="max-w-[1299px] mx-auto px-6 lg:px-12">
            <div className={`${styles.blogContent} ${styles.contentFadeIn} font-general`}>
              {/* Show remaining content after first paragraph */}
              <div
                dangerouslySetInnerHTML={{
                  __html: DOMPurify.sanitize(getRemainingContent(blog.content || blog.description || ''))
                }}
              />
            </div>
          </div>
        </div>

        {/* Related Blogs Section - Only show if blogs are visible */}
        {shouldShowBlogSection && <BlogCarouselSection />}

        {/* Footer */}
        <ClientFooter />
      </div>
    </ClientLayout>
  );
};

export default BlogDetailPage;
