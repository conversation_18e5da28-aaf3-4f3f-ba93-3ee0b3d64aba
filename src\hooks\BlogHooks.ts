import { useState, useEffect } from 'react';
import axios from 'axios';

interface BlogData {
  _id: string;
  blogName: string;
  blogSubheading?: string;
  description: string;
  content?: string;
  handle: string;
  featureImage?: string;
  isVisible: boolean;
  publishedDate?: string;
  author?: string;
  writer?: string;
  writerName?: string;
  tags?: string[];
}


export const useBlogListing = () => {
  const [data, setData] = useState<BlogData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        setIsLoading(true);
        setIsError(false);
        setError(null);

        // Call the actual API
        const response = await axios.get('http://localhost:5959/api/v1/blogs/visible');
        setData(response.data.data || response.data || []);
      } catch (err) {
        console.error('Error fetching blogs:', err);
        setIsError(true);
        setError('Failed to fetch blogs');
        // No fallback data - let the component handle empty state
        setData([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlogs();
  }, []);

  return { data, isLoading, isError, error };
};

export const useHomepageJournal = () => {
  const [data, setData] = useState<BlogData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchJournalBlogs = async () => {
      try {
        setIsLoading(true);
        setIsError(false);
        setError(null);

        // Call the homepage journal API
        const response = await axios.get('http://localhost:5959/api/v1/homepage-sections/public/journal');

        // Handle different possible response structures
        let blogData: any[] = [];

        // Check all possible nested structures
        const possiblePaths = [
          response.data,
          response.data?.data,
          response.data?.data?.journals, // This is where the data actually is!
          response.data?.blogs,
          response.data?.journal,
          response.data?.content,
          response.data?.items,
          response.data?.results,
          response.data?.posts
        ];

        for (const path of possiblePaths) {
          if (Array.isArray(path) && path.length > 0) {
            blogData = path;
            console.log('Found journal data:', blogData);
            console.log('First journal item structure:', blogData[0]);
            console.log('All journal items:', blogData);

            // Transform journal data to match BlogData interface if needed
            const transformedData = blogData.map((item: any) => {
              console.log('Processing journal item:', item);

              // If the item is already in the correct format, use it as is
              if (item.blogName && item.handle) {
                return {
                  ...item,
                  isVisible: item.isVisible !== false // Default to true if not specified
                };
              }
              // If the item has a nested blog structure, extract it
              if (item.blog) {
                return {
                  ...item.blog,
                  isVisible: item.isVisible !== false || item.blog.isVisible !== false
                };
              }

              // Return the item as is and let the component handle it
              return {
                ...item,
                isVisible: item.isVisible !== false
              };
            });

            console.log('Transformed journal data:', transformedData);
            setData(transformedData);
            return;
          }
        }

        setData(blogData);
      } catch (err) {
        console.error('Error fetching journal blogs:', err);
        setIsError(true);
        setError('Failed to fetch journal blogs');
        // No fallback data - let the component handle empty state
        setData([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchJournalBlogs();
  }, []);

  return { data, isLoading, isError, error };
};

export const useBlogDetail = (handle: string) => {
  const [data, setData] = useState<BlogData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!handle) return;

    const fetchBlogDetail = async () => {
      try {
        setIsLoading(true);
        setIsError(false);
        setError(null);

        // Call the actual API
        const response = await axios.get(`http://localhost:5959/api/v1/blogs/handle/${handle}`);
        const blogData = response.data.data || response.data;
        console.log('API Blog data for handle:', handle, blogData);
        setData(blogData);
      } catch (err) {
        console.error('Error fetching blog detail:', err);
        setIsError(true);
        setError('Failed to fetch blog details');
        // No fallback data - let the component handle empty state
        setData(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlogDetail();
  }, [handle]);

  return { data, isLoading, isError, error };
};

// Hook to check if blog section should be visible
export const useBlogSectionVisibility = () => {
  const { data: blogs, isLoading } = useHomepageJournal();

  // If still loading, hide section to prevent flash
  if (isLoading) {
    return false;
  }

  // If no data from API or API error, hide section
  if (!Array.isArray(blogs) || blogs.length === 0) {
    return false;
  }

  // If API returned data, check if any blogs are visible
  const hasVisibleBlogs = blogs.some((blog: any) => blog.isVisible === true);

  return hasVisibleBlogs;
};
