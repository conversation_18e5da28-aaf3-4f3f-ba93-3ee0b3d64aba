import Link from "next/link";
import Image from "next/image";

interface BlogData {
  _id: string;
  blogName: string;
  description: string;
  handle: string;
  featureImage?: string;
  isVisible: boolean;
  publishedDate?: string;
}

interface JournalCardProps {
  data: BlogData;
}

const JournalCard = ({ data }: JournalCardProps) => {
  const handle = data?.handle;
  const BlogListingImg = "/assets/images/client/hero-animation-one.gif"; // Using existing image as fallback

  return (
    <div className="rounded-[20px] hover:shadow-lg transition-shadow duration-300 ease-in-out">
      <Link href={`/blogs/${handle}`} className='group'>
        <div className='aspect-[372/205] overflow-hidden w-full rounded-tl-[20px] rounded-tr-[20px]'>
          <Image
            src={data?.featureImage || BlogListingImg}
            alt="journal-card-img"
            width={372}
            height={205}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
        </div>
        <div className='p-5 border border-f0f0f0 rounded-bl-[20px] rounded-br-[20px] bg-white'>
          <p className='font-general font-regular md:font-medium text-383838 text-[10px] md:text-xs uppercase tracking-wide'>Journal</p>
          <p className='font-general font-semibold text-383838 text-sm md:text-base line-clamp-1 mt-1'>{data?.blogName}</p>
          <div className='font-general font-medium text-383838 text-xs md:text-sm line-clamp-2 md:line-clamp-3 mt-2'>
            <div dangerouslySetInnerHTML={{ __html: data?.description }} />
          </div>
          {data?.publishedDate && (
            <p className="text-gray-500 text-xs mt-2">
              {new Date(data.publishedDate).toLocaleDateString('en-US', {
                day: 'numeric',
                month: 'short',
                year: 'numeric'
              })}
            </p>
          )}
        </div>
      </Link>
    </div>
  );
};

export default JournalCard;
